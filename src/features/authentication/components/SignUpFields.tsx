import { useState } from 'react'
import { useParams, useSearchParams } from 'react-router'
import Divider from '../../../common/components/Divider'
import InputGroup from '../../../common/components/InputGroup'
import TextError from '../../../common/components/TextError'
import TextInput from '../../../common/components/TextInput'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import {
  getSupportedTontinatorParams,
  sanitizeInputValue,
} from '../../../common/utils/UtilFunctions'
import TermsAndConditions from '../../agreements/pages/TermsAndConditions'
import { chooseDefaultParams } from '../../dashboard/hooks/usePreRegisterForecast'
import { useAddressForm, useUserInfo } from '../hooks/FormHooks'
import style from '../style/Register.module.scss'
import { SignUpFieldsProps } from '../types/SignUpFields.types'
import { excludeDomainFromUrl } from '../utils/UtilsFunctions'
import ModalSignUpNavigationButtons from './ModalSignUpNavigationButtons'
import PageSignUpNavigationButtons from './PageSignUpNavigationButtons'
import SignUpAdditionalInfo from './SignUpAdditionalInfo'
import SignUpBasicInfo from './SignUpBasicInfo'
import SignUpCheckboxes from './SignUpCheckboxes'

/**
 * Renders sign up form fields. The fields are validated if the
 * user has entered the data in the right format. If all the validation passes
 * then an object with the fields values is returned.
 */
const SignUpFields = ({
  forecastUserData,
  registerButtonLabel,
  forecastPageRegisterModal,
  hideAdditionalFields,
  hideDivider,
  backButtonAction,
  handleRegister,
  signUpError,
}: SignUpFieldsProps) => {
  const t = useTranslate()
  const { referral_code } = useParams()
  const [searchParams] = useSearchParams()
  const { detectedCountry } = useLocalization()

  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: detectedCountry?.alpha3,
  })
  const { tontinatorParams } = supportedCountry

  if (searchParams && searchParams.size > 0) {
    const forecastParams = chooseDefaultParams({
      tontinatorParams,
      urlSearchParams: searchParams,
      supportedCountry,
    })
    forecastUserData = {
      demographic_data_current_age: forecastParams?.contributionAge,
      demographic_data_country_of_residence: forecastParams?.countryOfResidence,
      demographic_data_sex: forecastParams?.sex,
      contribution_allocations: forecastParams?.strategy,
      contributions: {
        monthly_amount: forecastParams?.monthlyContribution ?? 0,
        onetime_amount: forecastParams?.oneTimeContribution ?? 0,
        payout_age: forecastParams?.retirementAge ?? { age: 0, month: 0 },
      },
    }
  }

  const [termsVersion, setTermsVersion] = useState<number>()
  const [termsAndConditions, setTermsAndConditions] = useState(false)
  const [renderTermsAndConditions, setRenderTermsAndConditions] =
    useState(false)
  const [emailUpdates, setEmailUpdates] = useState(false)

  const { userCountry, setUserCountry } = useAddressForm({
    country: detectedCountry?.alpha3,
  })

  const {
    userEmail,
    setUserEmail,
    setFirstName,
    firstName,
    lastName,
    setLastName,
    userSex,
    setSex,
    inputValidation,
    referralCode,
    setReferralCode,
    setCurrentAge,
    currentAge,
  } = useUserInfo({
    sex: forecastUserData?.demographic_data_sex ?? tontinatorParams?.defaultSex,
    referralCode: excludeDomainFromUrl(referral_code ?? ''),
    currAge:
      forecastUserData?.demographic_data_current_age?.age ??
      tontinatorParams?.defaultCurrentAgeSlider?.age,
  })

  const handleCountry = (country: string) => {
    setUserCountry(country)

    const defaultRetirementAge = getSupportedTontinatorParams(country)

    //Default to country default retirement age
    setCurrentAge(defaultRetirementAge?.defaultCurrentAgeSlider?.age)
  }

  const handleReferralCode = (referralCode: string) => {
    const modifiedReferralCode = excludeDomainFromUrl(referralCode)

    setReferralCode(sanitizeInputValue({ inputValue: modifiedReferralCode }))
  }

  const validatedAdditionalFields = Boolean(
    hideAdditionalFields || (userSex && userCountry)
  )

  const validatedRegisterInfo = Boolean(
    inputValidation.emailValidated?.valid &&
      inputValidation.firstNameValidated?.valid &&
      inputValidation.lastNameValidated?.valid &&
      termsAndConditions &&
      validatedAdditionalFields
  )

  const handleRegistration = () => {
    if (validatedRegisterInfo) {
      handleRegister({
        firstName,
        lastName,
        email: userEmail,
        sex: userSex,
        currentAge,
        country: userCountry,
        referralCode: referralCode,
        termsVersion,
        emailUpdates: emailUpdates,
        forecastUserData,
      })
    }
  }

  return (
    <>
      <InputGroup noStyle={forecastPageRegisterModal}>
        <SignUpBasicInfo
          firstName={firstName ?? ''}
          setFirstName={setFirstName}
          lastName={lastName ?? ''}
          setLastName={setLastName}
          userEmail={userEmail}
          setUserEmail={setUserEmail}
          {...inputValidation}
        />
        {!hideAdditionalFields && (
          <SignUpAdditionalInfo
            sex={userSex}
            setSex={setSex}
            currentAge={currentAge ?? 0}
            setCurrentAge={setCurrentAge}
            country={userCountry}
            handleCountry={handleCountry}
          />
        )}
        <TextInput
          value={referralCode}
          onChange={handleReferralCode}
          label={t('REFERRAL_LINK_OR_CODE')}
          labelInfoIcon
          tooltipText={t('TOOLTIP_MSG_REF_CODE')}
          optional
          validatorFunction={(referralCode) => {
            // We want to validate the referral code only, otherwise
            // when the user pastes the whole url, the validator function will
            // be validating the whole link instead only the referral code
            const cleanReferralCode = excludeDomainFromUrl(referralCode)
            // Overrides the function to do optional validation
            inputValidation.validateReferralCode(cleanReferralCode, true)
          }}
          errorMessage={inputValidation.referralCodeValidated}
          restrictionRegex={inputRestrictionRegex.referralCodeFormat}
          trackActivity={{
            trackId: 'register_referral_code',
          }}
        />
        {/* Empty div for spacing */}
        {!hideAdditionalFields && <div />}
      </InputGroup>

      {!hideDivider && <Divider className={style[`register__divider`]} />}
      <SignUpCheckboxes
        termsAndConditions={termsAndConditions}
        setTermsAndConditions={setTermsAndConditions}
        setRenderTermsAndConditions={setRenderTermsAndConditions}
        emailUpdates={emailUpdates}
        setEmailUpdates={setEmailUpdates}
        setTermsVersion={setTermsVersion}
      />
      {signUpError && (
        <TextError
          errorText={t('ERROR_HARD_FAIL_SIGNUP')}
          className={style['register__error-msg']}
        />
      )}
      {forecastPageRegisterModal ? (
        <ModalSignUpNavigationButtons
          forecastUserData={forecastUserData}
          registerButtonLabel={registerButtonLabel}
          forecastPageRegisterModal={forecastPageRegisterModal}
          backButtonAction={backButtonAction}
          validatedRegisterInfo={validatedRegisterInfo}
          handleRegistration={handleRegistration}
        />
      ) : (
        <PageSignUpNavigationButtons
          handleRegistration={handleRegistration}
          validatedRegisterInfo={validatedRegisterInfo}
          backButtonAction={backButtonAction}
          registerButtonLabel={t('BUTTON_LABEL.SIGN_UP')}
          forecastPageRegisterModal={forecastPageRegisterModal}
          forecastUserData={forecastUserData}
        />
      )}

      <TermsAndConditions
        isOpen={renderTermsAndConditions}
        onClickCloseModalButton={() => setRenderTermsAndConditions(false)}
        signature={{
          checkboxChecked: termsAndConditions,
          agreementVersion: termsVersion ?? 0,
        }}
        onSigned={(signature) => {
          setTermsAndConditions(signature?.checkboxChecked)
          setTermsVersion(signature?.agreementVersion)
        }}
      />
    </>
  )
}

export default SignUpFields
