import bottomStyle from '../style/BottomCtaLiteLayout.module.scss'
import style from '../style/PublicTontinatorPage.module.scss'
import { PublicTontinatorBottomSectionProps } from '../types/PublicTontinatorBottomSection.types'
import MtlCompareBtns from './MtlCompareBtns'
import PublicTontinatorHomeButtons from './MtlHomeButtons'

/**
 * Bottom CTA section for the PublicSandboxTontinatorPage that displays action buttons
 * for comparing plans, viewing scenarios, and signing up/in. Renders different button
 * layouts based on comparison state and provides secure window.open functionality for
 * external navigation.
 */
const PublicTontinatorBottomSection = ({
  isCompareOpen,
  setIsCompareOpen,
  setOpenSliderPage,
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  yellowForecastParams,
}: PublicTontinatorBottomSectionProps) => {
  return (
    <section className={style['public-tontinator-page__bottom-cta-container']}>
      <section className={bottomStyle['bottom-cta-lite-layout']}>
        {!isCompareOpen && (
          <PublicTontinatorHomeButtons
            incomeForecastParams={incomeForecastParams}
            onSeeOtherScenarios={() => {
              setOpenSliderPage(true)
            }}
            onCompareChoices={() => {
              setIsCompareOpen(true)
            }}
            onClickSignUpButton={() => {
              // Modern approach using window.open with security features
              const windowFeatures = 'noopener,noreferrer'
              const handle = window.open(
                'https://app.mytontine.com/signup',
                '_blank',
                windowFeatures
              )

              // Additional security: reset opener property
              if (handle) {
                handle.opener = null
              }
            }}
            setIsOpenSignInModal={() => {
              // Modern approach using window.open with security features
              const windowFeatures = 'noopener,noreferrer'
              const handle = window.open(
                'https://app.mytontine.com/signin',
                '_blank',
                windowFeatures
              )

              // Additional security: reset opener property
              if (handle) {
                handle.opener = null
              }
            }}
          />
        )}

        {isCompareOpen && (
          <MtlCompareBtns
            onClickPlan2={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(yellowForecastParams)
            }}
            onSeeOtherScenarios={() => {
              setOpenSliderPage(true)
            }}
            onClickBack={() => {
              setIsCompareOpen(false)
            }}
            blueForecastParams={blueForecastParams}
            yellowForecastParams={yellowForecastParams}
            onClickPlan1={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(blueForecastParams)
            }}
          />
        )}
      </section>
    </section>
  )
}

export default PublicTontinatorBottomSection
