import { IncomeForecastParams } from '../../../../common/types/CommonTypes.types'
import { TontinatorUIParams } from '../../../../common/types/SupportedCountries.types'
import { serializeIncomeForecastParams, parseUrlParametersCore } from '../usePreRegisterForecast'

describe('serializeIncomeForecastParams', () => {
  const mockTontinatorParams: TontinatorUIParams = {
    defaultRetirementAgeSlider: { age: 65, month: 0 },
    defaultCurrentAgeSlider: { age: 30, month: 0 },
    defaultOneTimeSliderValue: 10000,
    defaultMonthlySliderValue: 500,
    defaultSex: 'Male',
    minRetirementAge: { age: 55, month: 0 },
    maxRetirementAge: { age: 75, month: 0 },
    minCurrentAge: { age: 18, month: 0 },
    maxCurrentAge: { age: 70, month: 0 },
  }

  const mockDefaultParamsFromUi = {
    strategy: 'FII' as const,
    countryOfResidence: 'USA',
  }

  const sampleParams: IncomeForecastParams = {
    contributionAge: { age: 35, month: 0 },
    retirementAge: { age: 67, month: 0 },
    sex: 'Female',
    oneTimeContribution: 15000,
    monthlyContribution: 750,
    countryOfResidence: 'CAN',
    strategy: 'BOL',
    paramsMode: 'TTF',
  }

  it('should serialize IncomeForecastParams to URLSearchParams correctly', () => {
    const urlParams = serializeIncomeForecastParams(sampleParams)
    
    expect(urlParams.get('currAge')).toBe('35')
    expect(urlParams.get('retAge')).toBe('67')
    expect(urlParams.get('sex')).toBe('Female')
    expect(urlParams.get('oneTimeDep')).toBe('15000')
    expect(urlParams.get('monthlyDep')).toBe('750')
    expect(urlParams.get('country')).toBe('CAN')
    expect(urlParams.get('invStrat')).toBe('BOL')
  })

  it('should handle optional retirementAge field', () => {
    const paramsWithoutRetirement = { ...sampleParams }
    delete paramsWithoutRetirement.retirementAge
    
    const urlParams = serializeIncomeForecastParams(paramsWithoutRetirement)
    
    expect(urlParams.get('retAge')).toBeNull()
    expect(urlParams.get('currAge')).toBe('35')
  })

  it('should be the inverse of parseUrlParametersCore', () => {
    // Serialize the params
    const urlParams = serializeIncomeForecastParams(sampleParams)
    
    // Parse them back
    const parsedParams = parseUrlParametersCore({
      urlSearchParams: urlParams,
      tontinatorParams: mockTontinatorParams,
      defaultParamsFromUi: mockDefaultParamsFromUi,
    })
    
    // Verify the round-trip works (note: some fields may have defaults applied)
    expect(parsedParams).not.toBeNull()
    expect(parsedParams!.contributionAge.age).toBe(sampleParams.contributionAge.age)
    expect(parsedParams!.retirementAge!.age).toBe(sampleParams.retirementAge!.age)
    expect(parsedParams!.sex).toBe(sampleParams.sex)
    expect(parsedParams!.oneTimeContribution).toBe(sampleParams.oneTimeContribution)
    expect(parsedParams!.monthlyContribution).toBe(sampleParams.monthlyContribution)
    expect(parsedParams!.countryOfResidence).toBe(sampleParams.countryOfResidence)
    expect(parsedParams!.strategy).toBe(sampleParams.strategy)
  })

  it('should handle edge cases with zero values', () => {
    const edgeCaseParams: IncomeForecastParams = {
      contributionAge: { age: 18, month: 0 },
      retirementAge: { age: 55, month: 0 },
      sex: 'Male',
      oneTimeContribution: 0,
      monthlyContribution: 0,
      countryOfResidence: 'USA',
      strategy: 'FII',
    }
    
    const urlParams = serializeIncomeForecastParams(edgeCaseParams)
    
    expect(urlParams.get('oneTimeDep')).toBe('0')
    expect(urlParams.get('monthlyDep')).toBe('0')
  })

  it('should handle all supported investment strategies', () => {
    const strategies = ['BOL', 'FII', 'VBI', 'BTC', 'XAU'] as const
    
    strategies.forEach(strategy => {
      const params = { ...sampleParams, strategy }
      const urlParams = serializeIncomeForecastParams(params)
      expect(urlParams.get('invStrat')).toBe(strategy)
    })
  })

  it('should handle both sex types', () => {
    const maleParams = { ...sampleParams, sex: 'Male' as const }
    const femaleParams = { ...sampleParams, sex: 'Female' as const }
    
    const maleUrlParams = serializeIncomeForecastParams(maleParams)
    const femaleUrlParams = serializeIncomeForecastParams(femaleParams)
    
    expect(maleUrlParams.get('sex')).toBe('Male')
    expect(femaleUrlParams.get('sex')).toBe('Female')
  })
})
